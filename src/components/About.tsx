import React from 'react';
import type { PersonalInfo } from '../types/cv';

interface AboutProps {
  personalInfo: PersonalInfo;
  aboutText: string;
}

const About: React.FC<AboutProps> = ({ personalInfo, aboutText }) => {
  return (
    <section id="about" className="about">
      <div className="container">
        <div className="section-title">
          <h2 className="text-uppercase">Sobre mi</h2>
          <p>{aboutText}</p>
        </div>

        <div className="row d-flex align-items-center">
          <div className="col-lg-4" data-aos="fade-right">
            <img src={personalInfo.profileImage} className="img-fluid" alt={personalInfo.name} />
          </div>
          <div className="col-lg-8 pt-4 pt-lg-0 content" data-aos="fade-left">
            <h3>{personalInfo.title}</h3>
            <div className="row">
              <div className="col-lg-6">
                <ul>
                  <li>
                    <i className="bi bi-chevron-right"></i>
                    <strong>Teléfono:</strong>
                    <span>
                      <a href={`tel:${personalInfo.phone}`}>{personalInfo.phone}</a>
                    </span>
                  </li>
                  <li>
                    <i className="bi bi-chevron-right"></i>
                    <strong>Ciudad:</strong>
                    <span>{personalInfo.city}</span>
                  </li>
                </ul>
              </div>

              <div className="col-lg-6">
                <ul>
                  <li>
                    <i className="bi bi-chevron-right"></i>
                    <strong>Grado:</strong>
                    <span>{personalInfo.degree}</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
